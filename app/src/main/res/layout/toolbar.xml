<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.MaterialToolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/actual_toolbar_widget"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/white"
    app:titleTextColor="?attr/white"
    app:subtitleTextColor="?attr/white"
    app:navigationIconTint="?attr/white"
    app:itemTextAppearanceActive="@style/LightThemeInverted"
    app:itemTextAppearanceInactive="@style/LightThemeInverted"
    />