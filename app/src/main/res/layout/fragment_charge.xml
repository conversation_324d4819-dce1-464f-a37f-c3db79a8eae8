<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:orientation="vertical">

    <include layout="@layout/toolbar"
        android:id="@+id/toolbarCharge"
        android:layout_height="?attr/actionBarSize"
        android:layout_width="match_parent"/>

<androidx.core.widget.NestedScrollView
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/sale_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/promo_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"/>
        <LinearLayout
            android:id="@+id/charge_up"
            android:visibility="visible"
            android:clipChildren="false"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:baselineAligned="false">
            <RelativeLayout
                android:id="@+id/percent_layout"
                android:background="@drawable/white_block"
                android:padding="8dp"
                android:layout_width="0dp"
                android:layout_height="235dp"
                android:layout_marginBottom="14dp"
                android:layout_weight="1"
                android:rotation="0"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="14dp">
                <RelativeLayout
                    android:id="@+id/pus"
                    android:background="@drawable/grey_block_static"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="50sp"
                        android:textColor="?attr/colorr"
                        android:gravity="center"
                        android:id="@+id/text_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/charge_prog_bar_percent"
                        android:background="@drawable/circle_back"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="100"
                        android:progress="0"
                        android:layout_centerInParent="true"
                        app:indicatorColor="?attr/colorr"
                        app:indicatorInset="2dp"
                        app:indicatorSize="160dp"
                        app:trackColor="@color/empty"
                        app:trackCornerRadius="10dp"
                        app:trackThickness="3dp"/>
                </RelativeLayout>
            </RelativeLayout>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/time_num"
                android:background="@drawable/white_block"
                android:padding="8dp"
                android:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginBottom="14dp"
                android:layout_marginEnd="9dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:visibility="invisible"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="0"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <LinearLayout
                        android:padding="2dp"
                        android:layout_width="32dp"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/zero"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:text="@string/for_time_up_charge"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:id="@+id/day_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:id="@+id/imageView18"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_day"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/text_time_day"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/all_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="3dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_day_night"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/text_time_night"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/night_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_night"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/text_time_daynight"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        
        <!-- Not charging message -->
        <TextView
            android:id="@+id/not_charging_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:background="@drawable/white_block"
            android:padding="16dp"
            android:text="@string/not_charging_message"
            android:textAlignment="center"
            android:textColor="?attr/colorr"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/battery_alarm_btn"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/charge_alarm"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/da_view"/>
            <ImageView
                android:id="@+id/battery_wear_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/test_view"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/test_view"
                app:layout_constraintTop_toTopOf="@+id/test_view"/>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/test_view"
                android:focusableInTouchMode="true"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/damage_of_battery_charge"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="2dp"
                app:layout_constraintEnd_toStartOf="@+id/battery_wear_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/da_view"
                android:background="@drawable/grey_block_static"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/test_view">
                <ProgressBar
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:max="100"
                    android:progress="100"
                    android:progressDrawable="@drawable/progress_bar_grey"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/seekBar"
                    app:layout_constraintEnd_toEndOf="@+id/seekBar"
                    app:layout_constraintStart_toStartOf="@+id/seekBar"
                    app:layout_constraintTop_toTopOf="@+id/seekBar"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/damage_bar_seekwhite"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:max="100"
                    android:progress="70"
                    android:progressDrawable="@drawable/progress_bar_light_color"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/seekBar"
                    app:layout_constraintEnd_toEndOf="@+id/seekBar"
                    app:layout_constraintStart_toStartOf="@+id/seekBar"
                    app:layout_constraintTop_toTopOf="@+id/seekBar"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/damage_bar_percent"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:max="100"
                    android:progress="50"
                    android:progressDrawable="@drawable/progress_bar_empty_mycolor"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/seekBar"
                    app:layout_constraintEnd_toEndOf="@+id/seekBar"
                    app:layout_constraintStart_toStartOf="@+id/seekBar"
                    app:layout_constraintTop_toTopOf="@+id/seekBar"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <SeekBar
                    android:id="@+id/seekBar"
                    android:focusable="auto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:max="100"
                    android:progress="90"
                    android:progressDrawable="@drawable/seek_bar"
                    android:thumb="@drawable/thumb"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/var_damage_up4"/>
                <TextView
                    android:textSize="14sp"
                    android:ellipsize="marquee"
                    android:textColor="?attr/black"
                    android:gravity="center_horizontal"
                    android:id="@+id/var_damage_up4"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/calculated_capacity"
                    android:singleLine="true"
                    android:layout_centerHorizontal="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/cs_text"
                android:focusableInTouchMode="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/status_charge"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cs_2_1"
                android:background="@drawable/grey_block_line_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/cs_1">
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/c_text_ni_power"
                    android:visibility="invisible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/not_identified"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textedd"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <ProgressBar
                    android:id="@+id/progressBar_power_minus"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:max="150"
                    android:progress="70"
                    android:progressDrawable="@drawable/progress_bar_diss"
                    app:layout_constraintBottom_toBottomOf="@+id/progressBar_power"
                    app:layout_constraintEnd_toEndOf="@+id/progressBar_power"
                    app:layout_constraintStart_toStartOf="@+id/progressBar_power"
                    app:layout_constraintTop_toTopOf="@+id/progressBar_power"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/progressBar_power"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="150"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textedd"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_power_charging"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/p_text"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/textedd"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/p_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/watt"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/val_power_charging"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/textedd"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/power"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_power_charging"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cs_1"
                android:background="@drawable/grey_block_line_up_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                app:layout_constraintTop_toBottomOf="@+id/cs_text">
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/v_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/v"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/val_voltage"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <ProgressBar
                    android:id="@+id/progressBar_voltage"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="5500"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_remain_var22"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:gravity="end"
                    android:id="@+id/val_voltage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_alignBottom="@+id/text_remain_var22"
                    android:layout_marginStart="6dp"
                    android:layout_alignParentEnd="true"
                    app:layout_constraintEnd_toStartOf="@+id/v_text"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/text_remain_var22"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_var22"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/voltage"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_voltage"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cs_2"
                android:background="@drawable/grey_block_line_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/cs_2_1">
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/c_text_ni_ampere"
                    android:visibility="invisible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/not_identified"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_remain_var33"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <ProgressBar
                    android:id="@+id/progressBar_current_minus"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:max="2000"
                    android:progress="70"
                    android:progressDrawable="@drawable/progress_bar_diss"
                    app:layout_constraintBottom_toBottomOf="@+id/progressBar_current"
                    app:layout_constraintEnd_toEndOf="@+id/progressBar_current"
                    app:layout_constraintStart_toStartOf="@+id/progressBar_current"
                    app:layout_constraintTop_toTopOf="@+id/progressBar_current"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/progressBar_current"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="2000"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_remain_var33"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_currrent_charging"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/c_text"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/text_remain_var33"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/c_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/val_currrent_charging"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_var33"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/current_charging"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_currrent_charging"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cs_3"
                android:background="@drawable/grey_block_line_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/cs_2">
                <ProgressBar
                    android:id="@+id/progressBar_temp"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="700"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_remain_var44"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_temp2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/val_temp_text"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/text_remain_var44"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_temp_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text=" °C"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/val_temp2"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_var44"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/temperature"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_temp2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cs_4"
                android:background="@drawable/grey_block_line_down_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/cs_3">
                <ProgressBar
                    android:id="@+id/progressBar_averagespeed_grey"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="100"
                    android:progress="100"
                    android:progressDrawable="@drawable/progress_bar_grey"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_remain_var55"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/progressBar_averagespeed"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:max="100"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    app:layout_constraintBottom_toBottomOf="@+id/progressBar_averagespeed_grey"
                    app:layout_constraintEnd_toEndOf="@+id/progressBar_averagespeed_grey"
                    app:layout_constraintStart_toStartOf="@+id/progressBar_averagespeed_grey"
                    app:layout_constraintTop_toTopOf="@+id/progressBar_averagespeed_grey"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/speedcharge_percent_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/val_average_speed"
                    app:layout_constraintEnd_toEndOf="parent"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_average_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/speedcharge_percent_text"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/text_remain_var55"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_var55"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/average_speed_charge"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_average_speed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:id="@+id/remaining_table"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/rem_text"
                android:focusableInTouchMode="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/remaining_time_charge"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rem_1"
                android:background="@drawable/grey_block_line_up_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                app:layout_constraintTop_toBottomOf="@+id/rem_text">
                <ProgressBar
                    android:id="@+id/progressBar_remain_to_var2"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="100"
                    android:progress="100"
                    android:progressDrawable="@drawable/progress_bar_grey"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_remain_var"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/progressBar_remain_to_var"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:max="100"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    app:layout_constraintBottom_toBottomOf="@+id/progressBar_remain_to_var2"
                    app:layout_constraintEnd_toEndOf="@+id/progressBar_remain_to_var2"
                    app:layout_constraintStart_toStartOf="@+id/progressBar_remain_to_var2"
                    app:layout_constraintTop_toTopOf="@+id/progressBar_remain_to_var2"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_remain_to_var"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/text_remain_var"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_var"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/projected_time_charge_to_100"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    android:layout_alignParentStart="true"
                    app:layout_constraintEnd_toStartOf="@+id/val_remain_to_var"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rem_2"
                android:background="@drawable/grey_block_line_down_static"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/rem_1">
                <ProgressBar
                    android:id="@+id/progressBar_remain_to_100_2"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="6dp"
                    android:max="100"
                    android:progress="100"
                    android:progressDrawable="@drawable/progress_bar_grey"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rem_barrier"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <ProgressBar
                    android:id="@+id/progressBar_remain_to_100"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:max="100"
                    android:progress="0"
                    android:progressDrawable="@drawable/progress_bar"
                    app:layout_constraintBottom_toBottomOf="@+id/progressBar_remain_to_100_2"
                    app:layout_constraintEnd_toEndOf="@+id/progressBar_remain_to_100_2"
                    app:layout_constraintStart_toStartOf="@+id/progressBar_remain_to_100_2"
                    app:layout_constraintTop_toTopOf="@+id/progressBar_remain_to_100_2"
                    style="?android:attr/progressBarStyleHorizontal"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:id="@+id/val_remain_to_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/zero"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@+id/rem_val_barrier"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/rem_val_barrier"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:barrierDirection="end"
                    app:constraint_referenced_ids="text_remain_to_100, text_remain_to_100_charge"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_to_100_charge"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/projected_time_charge_to_100"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintEnd_toStartOf="@+id/val_remain_to_100"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_remain_to_100"
                    android:focusableInTouchMode="true"
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="@string/projected_time_charge_to_100"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintEnd_toStartOf="@+id/val_remain_to_100"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/rem_barrier"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="text_remain_to_100, text_remain_to_100_charge, val_remain_to_100"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:id="@+id/current_session_block"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <ImageView
                android:id="@+id/charge_session_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/st_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/st_text"
                app:layout_constraintTop_toTopOf="@+id/st_text"/>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/st_text"
                android:focusableInTouchMode="true"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/current_session"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="2dp"
                app:layout_constraintEnd_toStartOf="@+id/charge_session_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_6"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_5"
                app:layout_constraintTop_toBottomOf="@+id/s_4">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_day_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_day_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma_in_medium"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_charge_day_session2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_charge_day_session2"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/text_speed_charge_day_session2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_charge_day_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_day_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te77"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te77"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/active_mode"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_5"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_6"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_3">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma_in_medium"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_charge_night_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_charge_night_session"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_night_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_night_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/text_speed_charge_night_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_charge_night_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_night_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te88"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te88"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/screen_off"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_3"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_charge_all_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_charge_all_session"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_all_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_all_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/text_speed_charge_all_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_charge_all_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_all_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te99"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te99"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/average_speed"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_4"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_3"
                app:layout_constraintTop_toBottomOf="@+id/s_2">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/textView_percent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_session_last"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_session_last"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/charge_session_percent"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textView_percent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/textView_percent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/textView9"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_charge_day_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_charge_day_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/text_speed_charge_day_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_charge_session_last"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_session_last"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te0"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te0"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/all"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_2"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_1"
                app:layout_constraintTop_toBottomOf="@+id/st_text">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/textView7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/var_speedcharge_percent_now"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/var_speedcharge_percent_now"/>
                <TextView
                    android:textSize="14sp"
                    android:visibility="invisible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/var_speedcharge_percent_now"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/var_speedcharge_percent_now"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text226"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text226"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/now"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_1"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/st_text">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/time_charge_session_start"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_fulltime_charge_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_fulltime_charge_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero_seconds"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView6"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/textView6"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/all_time_charge"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/reset_session_charge_button"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/reset_sessions"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_5"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <RelativeLayout
            android:id="@+id/amperage_table"
            android:background="@drawable/white_block"
            android:paddingBottom="8dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:animateLayoutChanges="true"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/selector_amperage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_below="@+id/wear_rate_percent"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn_selector1"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/current_charging"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn_selector2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn_selector2"
                    android:background="@drawable/grey_block"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/power"
                    android:layout_marginStart="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btn_selector1"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/percent_graph_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_below="@+id/under_graph_percent"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn1"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/m8"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn2"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/m36"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn3"
                    app:layout_constraintStart_toEndOf="@+id/btn1"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn3"
                    android:background="@drawable/grey_block"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/full_time"
                    android:layout_marginStart="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btn2"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/wear_rate_percent"
                android:focusableInTouchMode="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/amperage_current_session"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="10dp"/>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/sdfsd"
                android:background="@drawable/grey_block"
                android:focusable="true"
                android:visibility="gone"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:layout_below="@+id/percent_graph_change"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:text="@string/graph_percentage_text_down"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="7dp"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/under_graph_percent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:baselineAligned="false"
                android:layout_below="@+id/graph_percent"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_7_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_6_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_5_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_4_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_3_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_2_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="bottom|center_horizontal"
                        android:id="@+id/day_1_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
            </LinearLayout>
            <RelativeLayout
                android:id="@+id/graph_percent"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_marginTop="7dp"
                android:foreground="@drawable/graph"
                android:layout_below="@+id/selector_amperage"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false">
                    <LinearLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5">
                        <TextView
                            android:textSize="5.5sp"
                            android:id="@+id/t26"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="900"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="800"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t3"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="700"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t4"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="600"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t5"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="500"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t6"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="400"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t7"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="300"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t8"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="200"/>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="6dp">
                        <TextView
                            android:textSize="11sp"
                            android:textColor="?attr/graph_text"
                            android:gravity="start"
                            android:id="@+id/t9"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="100"/>
                    </LinearLayout>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/line_horizontal"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5">
                        <TextView
                            android:textSize="5.5sp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>
                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/chart1_percent"
                    android:padding="1dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:foreground="@drawable/hide_corners_graph"
                    android:layout_centerHorizontal="true">
                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chart_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:rotationY="180"/>
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:id="@+id/indent_down"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/i_text"
                android:focusableInTouchMode="true"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/average_speed_charge"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="2dp"
                app:layout_constraintBottom_toTopOf="@+id/i1"
                app:layout_constraintEnd_toStartOf="@+id/charge_avg_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/charge_avg_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/i_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/i_text"
                app:layout_constraintTop_toTopOf="@+id/i_text"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i3"
                android:background="@drawable/grey_block_line_down_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/i2">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_3"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/text_speed_charge_all"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_3"
                    app:layout_constraintStart_toEndOf="@+id/per_3"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_3"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_all"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_all"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_all"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/complex_use"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/text_percent_charge_all"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i2"
                android:background="@drawable/grey_block_line_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/i1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_2"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/text_speed_charge_night"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_2"
                    app:layout_constraintStart_toEndOf="@+id/per_2"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_2"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_night"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_night"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_night"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/screen_off"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/text_percent_charge_night"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i1"
                android:background="@drawable/grey_block_line_up_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                app:layout_constraintTop_toBottomOf="@+id/i_text">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_1"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/text_speed_charge_day"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_1"
                    app:layout_constraintStart_toEndOf="@+id/per_1"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_1"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_charge_day"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_charge_day"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_charge_day"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/active_mode"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/text_percent_charge_day"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/update_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/update_view_btn"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:visibility="invisible"
                android:clickable="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:text=""
                android:singleLine="true"
                android:textAlignment="center"
                android:layout_marginEnd="4dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
</LinearLayout>
