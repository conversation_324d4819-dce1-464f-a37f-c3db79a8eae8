package com.tqhit.battery.one.fragment.main

import android.animation.ValueAnimator
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.SeekBar
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentChargeBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.BatteryUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject
import kotlin.math.absoluteValue
import kotlin.math.max
import kotlin.math.roundToInt
import kotlinx.coroutines.launch

/**
 * Deprecated charge fragment using the legacy BatteryViewModel.
 * 
 * @deprecated Use [com.tqhit.battery.one.features.charge.presentation.NewChargeFragment] instead, 
 * which follows clean architecture principles and has a more modular design.
 */
@Deprecated(
    message = "Use NewChargeFragment from the features.charge.presentation package instead",
    replaceWith = ReplaceWith(
        "com.tqhit.battery.one.features.charge.presentation.NewChargeFragment",
        "com.tqhit.battery.one.features.charge.presentation.NewChargeFragment"
    )
)
@AndroidEntryPoint
class ChargeFragment : AdLibBaseFragment<FragmentChargeBinding>() {
    // region Properties
    private val TAG = "ChargeFragment"
    override val binding by lazy { FragmentChargeBinding.inflate(layoutInflater) }
    
    private val batteryViewModel: BatteryViewModel by viewModels()
    private val appViewModel: AppViewModel by viewModels()
    @Inject lateinit var vibrationService: VibrationService
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    
    private var currentPercentage = 0.0
    private var isChargingPrevState = false
    // endregion

    // region Permission Handling
    private val permissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (isGranted) {
                    showBatteryAlarmDialog()
                } else {
                    NotificationDialog(
                                    requireActivity(),
                                    getString(R.string.notification),
                                    getString(R.string.notify_access)
                            )
                            .show()
                }
            }
    // endregion

    // region Lifecycle Methods
    override fun setupData() {
        super.setupData()
        Log.d(TAG, "Setting up data in deprecated ChargeFragment - use NewChargeFragment instead")
        initializeBatteryPercentage()
        observeBatteryUpdates()
        observeChargingStatus()
        observeChargingTime()
        observeScreenTime()
        observeChargeSession()
        observeAverageChargeSpeedAllSession()

        // Initialize UI state based on current charging state
        updateUIBasedOnChargingState(batteryViewModel.isCharging.value)

        // Log initial state
        val initialPower = batteryViewModel.power.value
        val initialAmperage = batteryViewModel.amperage.value
        val isCharging = batteryViewModel.isCharging.value
        val batteryPct = batteryViewModel.batteryPercentage.value
        Log.d(TAG, "Initial fragment state: isCharging=$isCharging, batteryPct=$batteryPct%, power=${initialPower}W, amperage=${initialAmperage}mA")
    }

    override fun setupListener() {
        super.setupListener()
        setupInfoBlockListeners()
        setupBatteryAlarmListener()
    }

    override fun setupUI() {
        super.setupUI()
        setupSeekBar()
        setupToolbar()
        // Ensure proper UI state on fragment creation/recreation
        updateUIBasedOnChargingState(batteryViewModel.isCharging.value)
    }

    private fun setupToolbar() {
        val toolbarLayoutBinding = binding.toolbarCharge
        val actualToolbar = toolbarLayoutBinding.actualToolbarWidget

        (activity as? AppCompatActivity)?.setSupportActionBar(actualToolbar)

        (activity as? AppCompatActivity)?.supportActionBar?.apply {
            title = getString(R.string.charge)

            setDisplayHomeAsUpEnabled(true)
        }

        actualToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
    }

    // endregion

    // region UI State Management
    /**
     * Updates the UI elements based on whether the device is charging or not
     */
    private fun updateUIBasedOnChargingState(isCharging: Boolean) {


        // Always update the UI state regardless of previous state
        // to ensure consistency when navigating between fragments
        
        try {
            // Find views by ID if they're not accessible through the binding directly
            val notChargingMessage = binding.root.findViewById<TextView>(R.id.not_charging_message)
            
            // Update UI visibility based on charging state
            if (!isCharging) {
                // Show not charging message
                notChargingMessage?.visibility = View.VISIBLE
                
                // Hide charging-related UI sections
                binding.remainingTable?.visibility = View.GONE
                binding.currentSessionBlock?.visibility = View.GONE
                binding.indentDown?.visibility = View.GONE
                binding.amperageTable?.visibility = View.GONE
                
                // Update the charging status header but keep it visible
                binding.csText?.text = getString(R.string.status_charge) + " (Not Charging)"
            } else {
                // Hide message and show all charging-related UI
                notChargingMessage?.visibility = View.GONE
                binding.remainingTable?.visibility = View.VISIBLE
                binding.currentSessionBlock?.visibility = View.VISIBLE
                binding.indentDown?.visibility = View.VISIBLE
                binding.amperageTable?.visibility = View.VISIBLE
                
                // Reset charging status header
                binding.csText?.text = getString(R.string.status_charge)
            }
            
            // Store the current state
            isChargingPrevState = isCharging
            
            Log.d(TAG, "UI updated for charging state: isCharging=$isCharging")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating UI state: ${e.message}")
        }
    }
    // endregion

    // region Initialization
    private fun initializeBatteryPercentage() {
        lifecycleScope.launch {
            val initialPercentage = batteryViewModel.batteryPercentage.value
            currentPercentage = initialPercentage.toDouble()
            Log.d(TAG, "Initializing battery percentage: $initialPercentage%")
            binding.textPercent.text = buildString {
                append(initialPercentage.toString())
                append("%")
            }
            binding.chargeProgBarPercent.progress = initialPercentage.toInt()
        }
    }
    // endregion

    // region UI Listeners
    private fun setupInfoBlockListeners() {
        binding.dayBlock.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.day_info)
            }
        }

        binding.allBlock.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.all_info)
            }
        }

        binding.nightBlock.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.night_info)
            }
        }

        binding.batteryWearInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.battery_wear_info_up, R.string.battery_wear_info)
            }
        }

        binding.chargeSessionInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.current_session, R.string.current_session_info)
            }
        }

        binding.chargeAvgInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.average_speed_charge, R.string.avg_charge_info)
            }
        }
    }

    private fun setupBatteryAlarmListener() {
        binding.batteryAlarmBtn.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showBatteryAlarmDialog()
            }
        }
    }

    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
        NotificationDialog(requireContext(), getString(titleResId), getString(messageResId)).show()
    }
    // endregion

    // region Battery Alarm Dialog
    private fun showBatteryAlarmDialog() {
        SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show()
    }
    // endregion

    // region SeekBar Management
    private fun setupSeekBar() {
        val selectedPercent = batteryViewModel.getSelectedPercent()
        binding.seekBar.progress = selectedPercent
        binding.damageBarSeekwhite.progress = selectedPercent
        updateDamageText(selectedPercent)

        binding.seekBar.setOnSeekBarChangeListener(
                object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(
                            seekBar: SeekBar,
                            progress: Int,
                            fromUser: Boolean
                    ) {
                        binding.damageBarSeekwhite.progress = progress
                        updateDamageText(progress)
                    }

                    override fun onStartTrackingTouch(seekBar: SeekBar) {}

                    override fun onStopTrackingTouch(seekBar: SeekBar) {
                        batteryViewModel.setSelectedPercent(seekBar.progress)
                    }
                }
        )

        observeSelectedPercentage()
    }

    private fun observeSelectedPercentage() {
        lifecycleScope.launch {
            batteryViewModel.selectedPercent.collect { percent ->
                binding.seekBar.progress = percent
                binding.damageBarSeekwhite.progress = percent
                updateDamageText(percent)
            }
        }
    }

    private fun updateDamageText(percent: Int) {
        binding.varDamageUp4.text =
                getString(
                        R.string.charge_to,
                        percent.toString(),
                        BatteryUtils.calculateWearCycles(
                                        batteryViewModel.batteryPercentage.value.toInt(),
                                        percent
                                )
                                .toString()
                )
    }
    // endregion

    // region Battery Monitoring
    private fun observeBatteryUpdates() {
        lifecycleScope.launch {
            batteryViewModel.batteryPercentage.collect { percentage ->
                Log.d(TAG, "Battery percentage updated: $percentage%, isCharging=${batteryViewModel.isCharging.value}")
                if (percentage.toInt().toDouble() != currentPercentage) {
                    animateBatteryUpdate(percentage.toInt())
                }
                binding.damageBarPercent.progress = percentage.toInt()
            }
        }
        
        // Observe charging state changes and update UI accordingly
        lifecycleScope.launch {
            batteryViewModel.isCharging.collect { isCharging ->
                Log.d(TAG, "Charging state changed: isCharging=$isCharging, power=${batteryViewModel.power.value}W, amperage=${batteryViewModel.amperage.value}mA")
                
                // Update UI visibility based on charging state
                updateUIBasedOnChargingState(isCharging)
                
                // Log additional details when charging state changes
                val batteryPct = batteryViewModel.batteryPercentage.value
                val chargingRate = batteryViewModel.chargingRate.value
                val voltage = batteryViewModel.voltage.value
                val timeRemaining = BatteryUtils.formatTimeToHoursMinutesFromMillis(batteryViewModel.chargingTimeRemaining.value)
                
                Log.d(TAG, "Battery details when charging state changed: " +
                        "percentage=$batteryPct%, " +
                        "chargingRate=$chargingRate%/h, " +
                        "voltage=$voltage V, " +
                        "timeRemaining=$timeRemaining")
            }
        }
    }

    private fun animateBatteryUpdate(newPercentage: Int) {
        val animator = ValueAnimator.ofInt(currentPercentage.toInt(), newPercentage)
        animator.duration = 500
        animator.interpolator = AccelerateDecelerateInterpolator()

        animator.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            binding.textPercent.text = buildString {
                append(animatedValue.toString())
                append("%")
            }
            binding.chargeProgBarPercent.progress = animatedValue
        }

        animator.start()
        currentPercentage = newPercentage.toDouble()
    }

    // region Charging Status Monitoring
    private fun observeChargingStatus() {
        Log.d(TAG, "Setting up charging status observers")
        observeSelectedPercent()
        observeVoltage()
        observePower()
        observeAmperage()
        observeTemperature()
        observeChargingRate()
    }

    private fun observeSelectedPercent() {
        lifecycleScope.launch {
            batteryViewModel.selectedPercent.collect { percent ->
                binding.textRemainVar.text =
                        getString(
                                R.string.projected_time_charge_to_var,
                                batteryViewModel.getSelectedPercent().toString()
                        )
            }
        }
    }

    private fun observeVoltage() {
        lifecycleScope.launch {
            batteryViewModel.voltage.collect { voltage ->
                Log.d(TAG, "Voltage updated: $voltage V, isCharging=${batteryViewModel.isCharging.value}")
                binding.valVoltage.text = String.format(buildString { append("%.3f") }, voltage)
                binding.progressBarVoltage.progress = (voltage * 1000f).absoluteValue.roundToInt()
            }
        }
    }

    private fun observePower() {
        lifecycleScope.launch {
            batteryViewModel.power.collect { power ->
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Power updated: $power W (${if(power >= 0) "charging" else "discharging"}), isCharging=$isCharging")
                
                // Only update power UI if actually charging
                if (isCharging) {
                    binding.valPowerCharging.text = String.format(buildString { append("%.2f") }, power)
                    val progress = (power * 10).roundToInt()
                    binding.progressBarPower.progress = progress.absoluteValue
                    binding.progressBarPower.progressDrawable =
                            ContextCompat.getDrawable(
                                    requireContext(),
                                    if (power >= 0) R.drawable.progress_bar
                                    else R.drawable.progress_bar_diss
                            )
                }
            }
        }
    }

    private fun observeAmperage() {
        lifecycleScope.launch {
            batteryViewModel.amperage.collect { amperage ->
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Amperage updated: $amperage mA, isCharging=$isCharging")
                
                // Only update amperage UI if actually charging
                if (isCharging) {
                    binding.valCurrrentCharging.text = amperage.toInt().toString()
                    val progress = amperage.roundToInt()
                    binding.progressBarCurrent.progress = progress.absoluteValue
                    binding.progressBarCurrent.progressDrawable =
                            ContextCompat.getDrawable(
                                    requireContext(),
                                    if (amperage >= 0) R.drawable.progress_bar
                                    else R.drawable.progress_bar_diss
                            )
                }
            }
        }
    }

    private fun observeTemperature() {
        lifecycleScope.launch {
            batteryViewModel.temperature.collect { temp ->
                binding.valTemp2.text = String.format(buildString { append("%.1f") }, temp)
                binding.progressBarTemp.progress = (temp * 10).roundToInt()
            }
        }
    }

    private fun observeChargingRate() {
        lifecycleScope.launch {
            batteryViewModel.chargingRate.collect { rate ->
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Charging rate updated: $rate %/h, isCharging=$isCharging")
                
                // Only update charging rate UI if actually charging
                if (isCharging) {
                    binding.valAverageSpeed.text =
                            String.format(buildString { append("%.1f") }, if (rate > 0) rate else 0.0)
                    binding.progressBarAveragespeed.progress = if (rate > 0) rate.roundToInt() else 100
                    binding.progressBarAveragespeed.progressDrawable =
                            ContextCompat.getDrawable(
                                    requireContext(),
                                    if (rate > 0) R.drawable.progress_bar
                                    else R.drawable.progress_bar_light_color
                            )
                }
            }
        }
    }
    // endregion

    // region Charging Time Monitoring
    private fun observeChargingTime() {
        Log.d(TAG, "Setting up charging time observers")
        lifecycleScope.launch {
            batteryViewModel.chargingTimeRemaining.collect { time ->
                val formattedTime = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Time remaining to 100%: $formattedTime, isCharging=$isCharging")
                
                // Only update time remaining UI if actually charging
                if (isCharging) {
                    binding.valRemainTo100.text = formattedTime
                    binding.progressBarRemainTo100.progress =
                            if (time > 0) BatteryUtils.getTotalMinutesFromMillis(time).toInt() else 100
                    binding.progressBarRemainTo100.progressDrawable =
                            ContextCompat.getDrawable(
                                    requireContext(),
                                    if (time > 0) R.drawable.progress_bar
                                    else R.drawable.progress_bar_light_color
                            )
                }
            }
        }
        lifecycleScope.launch {
            batteryViewModel.chargingTimeRemainingToTarget.collect { time ->
                val formattedTime = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                val targetPercent = batteryViewModel.selectedPercent.value
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Time remaining to $targetPercent%: $formattedTime, isCharging=$isCharging")
                
                // Only update target time remaining UI if actually charging
                if (isCharging) {
                    binding.valRemainToVar.text = formattedTime
                    binding.progressBarRemainToVar.progress =
                            if (time > 0) BatteryUtils.getTotalMinutesFromMillis(time).toInt() else 100
                    binding.progressBarRemainToVar.progressDrawable =
                            ContextCompat.getDrawable(
                                    requireContext(),
                                    if (time > 0) R.drawable.progress_bar
                                    else R.drawable.progress_bar_light_color
                            )
                }
            }
        }
        lifecycleScope.launch {
            batteryViewModel.selectedPercent.collect { percent ->
                binding.textRemainVar.text =
                        getString(R.string.projected_time_charge_to_var, percent.toString())
            }
        }
    }
    // endregion

    // region Screen Time Monitoring
    private fun observeScreenTime() {
        lifecycleScope.launch {
            batteryViewModel.screenOnTimeRemaining.collect { time ->
                val formattedTime = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                Log.d(TAG, "Screen on time remaining: $formattedTime")
                binding.textTimeDay.text = formattedTime
            }
        }
        lifecycleScope.launch {
            batteryViewModel.usageStyleTimeRemaining.collect { time ->
                binding.textTimeNight.text = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
            }
        }
        lifecycleScope.launch {
            batteryViewModel.screenOffTimeRemaining.collect { time ->
                binding.textTimeDaynight.text =
                        BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
            }
        }
    }
    // endregion

    // region Charge Session Monitoring
    private fun observeChargeSession() {
        lifecycleScope.launch {
            batteryViewModel.startTimeSession.collect { time ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textFulltimeChargeSession.text =
                            BatteryUtils.formatElapsedTime(
                                    max(0, batteryViewModel.endTimeSession.value - time)
                            )
                    val (startTime, endTime) = BatteryUtils.formatSessionTimeRange(time, 0)
                    val textString =
                            if (startTime != null &&
                                    (endTime == null || batteryViewModel.isCharging.value)
                            ) {
                                getString(R.string.since, startTime)
                            } else if (startTime != null && endTime != null) {
                                getString(R.string.since_above, startTime, endTime)
                            } else {
                                ""
                            }
                    binding.timeChargeSessionStart.text = textString
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.endTimeSession.collect { time ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textFulltimeChargeSession.text =
                            BatteryUtils.formatElapsedTime(
                                    time - batteryViewModel.startTimeSession.value
                            )
                    val (startTime, endTime) =
                            BatteryUtils.formatSessionTimeRange(
                                    batteryViewModel.startTimeSession.value,
                                    time
                            )
                    val textString =
                            if (startTime != null &&
                                    (endTime == null || batteryViewModel.isCharging.value)
                            ) {
                                getString(R.string.since, startTime)
                            } else if (startTime != null && endTime != null) {
                                getString(R.string.since_above, startTime, endTime)
                            } else {
                                ""
                            }
                    binding.timeChargeSessionStart.text = textString
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageSpeedSession.collect { rate ->
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Session average charging speed: $rate %/h")
                if (isCharging) {
                    binding.textPercentChargeAllSession.text = String.format("%.1f", rate)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageSpeedMilliAmperesSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeAllSession.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.endPercentSession.collect { percent ->
                val isCharging = batteryViewModel.isCharging.value
                val startPercent = batteryViewModel.startPercentSession.value
                Log.d(TAG, "Session battery percent change: $startPercent → $percent")
                if (isCharging) {
                    binding.chargeSessionPercent.text = " ($startPercent → $percent)"
                    binding.textPercentChargeSessionLast.text =
                            (percent - startPercent).toInt().toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.totalMilliAmperesSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeDaySession.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.screenOffAverageSpeedSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textPercentChargeNightSession.text = String.format("%.1f", value)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.screenOffMilliAmperesSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeNightSession.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.screenOnAverageSpeedSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textPercentChargeDaySession.text = String.format("%.1f", value)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.screenOnMilliAmperesSession.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeDaySession2.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.rightNowPercentPerHourSession.collect { rate ->
                val isCharging = batteryViewModel.isCharging.value
                Log.d(TAG, "Current charging rate: $rate %/h, isCharging=$isCharging")
                if (isCharging) {
                    binding.varSpeedchargePercentNow.text = String.format("%.1f", rate)
                }
            }
        }
    }

    private fun observeAverageChargeSpeedAllSession() {
        lifecycleScope.launch {
            batteryViewModel.averageSpeed.collect { rate ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textPercentChargeAll.text = String.format("%.1f", rate)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageMilliAmperes.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeAll.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOffSpeed.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textPercentChargeNight.text = String.format("%.1f", value)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOffMilliAmperes.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeNight.text = value.toString()
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOnSpeed.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textPercentChargeDay.text = String.format("%.1f", value)
                }
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOnMilliAmperes.collect { value ->
                val isCharging = batteryViewModel.isCharging.value
                if (isCharging) {
                    binding.textSpeedChargeDay.text = value.toString()
                }
            }
        }
    }
    // endregion
}
