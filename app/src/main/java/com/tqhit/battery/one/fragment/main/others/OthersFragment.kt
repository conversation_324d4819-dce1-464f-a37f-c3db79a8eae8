package com.tqhit.battery.one.fragment.main.others

import android.content.res.Resources
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentOthersBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration
import com.tqhit.battery.one.fragment.main.others.adapter.GridItemData
import com.tqhit.battery.one.fragment.main.others.adapter.OthersGridAdapter
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment that displays navigation options to other app sections.
 * Integrates with CoreBatteryStatsService for real-time battery state monitoring
 * and DynamicNavigationManager for consistent navigation behavior.
 *
 * Features:
 * - Dynamic charge/discharge navigation based on battery state
 * - Anti-theft functionality configuration
 * - Navigation to Health and Settings fragments
 * - Proper lifecycle management and error handling
 */
@AndroidEntryPoint
class OthersFragment : AdLibBaseFragment<FragmentOthersBinding>() {

    override val binding by lazy {
        FragmentOthersBinding.inflate(layoutInflater)
    }

    // Dependencies injected via Hilt
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    @Inject
    lateinit var dynamicNavigationManager: DynamicNavigationManager

    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    // ViewModels
    private val appViewModel: AppViewModel by viewModels()

    // UI Components
    private lateinit var gridAdapter: OthersGridAdapter
    private val currentDisplayItems = mutableListOf<GridItemData>()

    // State tracking
    private var isDeviceCharging: Boolean = false

    companion object {
        private const val CHARGE_ITEM_ID = "ChargeFragment"
        private const val DISCHARGE_ITEM_ID = "dischargeFragment"
        private const val HEALTH_ITEM_ID = "healthFragment"
        private const val SETTINGS_ITEM_ID = "settingsFragment"
        private const val TAG = "OthersFragment"

        // Icon resource IDs (not const since they're not compile-time constants)
        private val ICON_WHEN_CHARGING = R.drawable.ic_discharge_icon
        private val ICON_WHEN_NOT_CHARGING = R.drawable.ic_charge_icon
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated: Initializing OthersFragment")

        try {
            initializeBatteryStateMonitoring()
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing battery state monitoring", e)
            // Set default state if initialization fails
            isDeviceCharging = false
        }
    }

    /**
     * Initializes battery state monitoring using CoreBatteryStatsProvider.
     * Replaces direct battery API usage with centralized battery monitoring.
     */
    private fun initializeBatteryStateMonitoring() {
        Log.d(TAG, "Initializing battery state monitoring via CoreBatteryStatsProvider")

        // Get initial state synchronously
        val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
        isDeviceCharging = currentStatus?.isCharging ?: false
        Log.d(TAG, "Initial charging state from CoreBatteryStatsProvider: $isDeviceCharging")

        // Start observing battery state changes
        startBatteryStateObservation()
    }

    /**
     * Starts observing battery state changes from CoreBatteryStatsProvider.
     * Uses proper lifecycle-aware coroutines for safe observation.
     */
    private fun startBatteryStateObservation() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                try {
                    coreBatteryStatsProvider.coreBatteryStatusFlow
                        .filterNotNull()
                        .distinctUntilChanged { old, new -> old.isCharging == new.isCharging }
                        .collect { batteryStatus ->
                            handleBatteryStateChange(batteryStatus.isCharging)
                        }
                } catch (e: Exception) {
                    Log.e(TAG, "Error observing battery status from CoreBatteryStatsProvider", e)
                }
            }
        }
    }

    /**
     * Handles battery state changes with proper error handling and logging.
     */
    private fun handleBatteryStateChange(newChargingState: Boolean) {
        if (isDeviceCharging != newChargingState) {
            Log.d(TAG, "Battery charging state changed: $isDeviceCharging -> $newChargingState")
            isDeviceCharging = newChargingState

            try {
                updateChargeItemInList()
            } catch (e: Exception) {
                Log.e(TAG, "Error updating charge item in list", e)
            }
        }
    }

    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "setupUI: Configuring UI components")

        try {
            prepareAdapterItems()
            setupGridAdapter()
            setupRecyclerView()
            setupAntiThiefFeatures()
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up UI", e)
        }
    }

    /**
     * Sets up the grid adapter with proper click handling.
     */
    private fun setupGridAdapter() {
        gridAdapter = OthersGridAdapter(currentDisplayItems) { clickedItem ->
            handleItemClick(clickedItem.id)
        }
        Log.d(TAG, "Grid adapter initialized with ${currentDisplayItems.size} items")
    }

    /**
     * Configures the RecyclerView with proper layout and decorations.
     */
    private fun setupRecyclerView() {
        binding.othersRecyclerView.apply {
            val spanCount = 1
            layoutManager = GridLayoutManager(requireContext(), spanCount)

            val spacingInDp = 32
            val spacingInPixels = (spacingInDp * Resources.getSystem().displayMetrics.density).toInt()

            // Remove existing decorations to prevent duplicates
            if (itemDecorationCount > 0) {
                removeItemDecorationAt(0)
            }

            addItemDecoration(GridSpacingItemDecoration(spanCount, spacingInPixels))
            adapter = gridAdapter
        }
        Log.d(TAG, "RecyclerView configured successfully")
    }

    /**
     * Sets up all anti-thief related features.
     */
    private fun setupAntiThiefFeatures() {
        setupAntiThiefInfoButton()
        setupAntiThiefSwitches()
    }

    /**
     * Sets up the anti-thief information button with proper error handling.
     */
    private fun setupAntiThiefInfoButton() {
        binding.antiThiefInfo.setOnClickListener {
            try {
                applovinInterstitialAdManager.showInterstitialAd(
                    "default_iv",
                    requireActivity(),
                ) {
                    showAntiThiefInfoDialog()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error showing interstitial ad for anti-thief info", e)
                // Show dialog directly if ad fails
                showAntiThiefInfoDialog()
            }
        }
    }

    /**
     * Shows the anti-thief information dialog.
     */
    private fun showAntiThiefInfoDialog() {
        try {
            NotificationDialog(
                context = requireContext(),
                title = getString(R.string.anti_thief),
                message = getString(R.string.anti_thief_info),
            ).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error showing anti-thief info dialog", e)
        }
    }

    /**
     * Sets up anti-thief switches with proper error handling and validation.
     */
    private fun setupAntiThiefSwitches() {
        try {
            // Initialize switch state
            binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()

            // Set up switch listener with proper error handling
            binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
                handleAntiThiefSwitchChange(isChecked)
            }

            Log.d(TAG, "Anti-thief switches configured successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up anti-thief switches", e)
        }
    }

    /**
     * Handles anti-thief switch state changes with proper validation.
     */
    private fun handleAntiThiefSwitchChange(isChecked: Boolean) {
        try {
            if (isChecked) {
                enableAntiThiefFeature()
            } else {
                disableAntiThiefFeature()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling anti-thief switch change", e)
            // Reset switch to previous state on error
            binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        }
    }

    /**
     * Enables anti-thief feature with password setup if needed.
     */
    private fun enableAntiThiefFeature() {
        if (!appViewModel.isAntiThiefPasswordSet()) {
            showPasswordSetupDialog()
        } else {
            appViewModel.setAntiThiefEnabled(true)
            Log.d(TAG, "Anti-thief feature enabled")
        }
    }

    /**
     * Disables anti-thief feature and clears password.
     */
    private fun disableAntiThiefFeature() {
        appViewModel.setAntiThiefPassword("")
        appViewModel.setAntiThiefEnabled(false)
        Log.d(TAG, "Anti-thief feature disabled")
    }

    /**
     * Shows password setup dialog for anti-thief feature.
     */
    private fun showPasswordSetupDialog() {
        try {
            SetupPasswordDialog(
                context = requireContext(),
                onConfirm = { password ->
                    handlePasswordSetup(password)
                },
                onCancel = {
                    handlePasswordSetupCancel()
                }
            ).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error showing password setup dialog", e)
            binding.switchEnableAntiThief.isChecked = false
        }
    }

    /**
     * Handles password setup confirmation.
     */
    private fun handlePasswordSetup(password: String) {
        if (password.isNotBlank()) {
            appViewModel.setAntiThiefPassword(password)
            appViewModel.setAntiThiefEnabled(true)
            binding.switchEnableAntiThief.isChecked = true
            Log.d(TAG, "Anti-thief password set successfully")
        } else {
            binding.switchEnableAntiThief.isChecked = false
            Log.w(TAG, "Empty password provided for anti-thief setup")
        }
    }

    /**
     * Handles password setup cancellation.
     */
    private fun handlePasswordSetupCancel() {
        binding.switchEnableAntiThief.isChecked = false
        Log.d(TAG, "Anti-thief password setup cancelled")
    }

    /**
     * Handles item click events with proper navigation logic.
     * Integrates with DynamicNavigationManager for consistent behavior.
     */
    private fun handleItemClick(itemId: String) {
        Log.d(TAG, "Item clicked: $itemId, charging state: $isDeviceCharging")

        try {
            when (itemId) {
                CHARGE_ITEM_ID -> {
                    navigateToChargeDischargeFragment()
                }
                HEALTH_ITEM_ID -> {
                    navigateToHealthFragment()
                }
                SETTINGS_ITEM_ID -> {
                    navigateToSettingsFragment()
                }
                else -> {
                    Log.w(TAG, "Unknown item clicked: $itemId")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling item click for $itemId", e)
        }
    }

    /**
     * Navigates to appropriate charge/discharge fragment based on current battery state.
     * This fixes the critical bug where CHARGE_ITEM_ID incorrectly navigated to HealthFragment.
     */
    private fun navigateToChargeDischargeFragment() {
        try {
            // Use DynamicNavigationManager for consistent navigation behavior
            if (::dynamicNavigationManager.isInitialized && dynamicNavigationManager.isInitialized()) {
                val targetItemId = if (isDeviceCharging) {
                    R.id.dischargeFragment
                } else {
                    R.id.chargeFragment
                }

                val handled = dynamicNavigationManager.handleUserNavigation(targetItemId)
                if (handled) {
                    Log.d(TAG, "Navigation delegated to DynamicNavigationManager for item: $targetItemId")
                } else {
                    Log.w(TAG, "DynamicNavigationManager could not handle navigation, falling back to direct navigation")
                    fallbackToDirectNavigation()
                }
            } else {
                Log.w(TAG, "DynamicNavigationManager not available, using direct navigation")
                fallbackToDirectNavigation()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error navigating to charge/discharge fragment", e)
            fallbackToDirectNavigation()
        }
    }

    /**
     * Fallback method for direct navigation when DynamicNavigationManager is not available.
     */
    private fun fallbackToDirectNavigation() {
        try {
            val targetFragment = if (isDeviceCharging) {
                DischargeFragment()
            } else {
                StatsChargeFragment()
            }
            navigateToFragment(targetFragment)
            Log.d(TAG, "Direct navigation to ${targetFragment::class.simpleName}")
        } catch (e: Exception) {
            Log.e(TAG, "Error in fallback navigation", e)
        }
    }

    /**
     * Navigates to Health fragment using DynamicNavigationManager.
     */
    private fun navigateToHealthFragment() {
        try {
            if (::dynamicNavigationManager.isInitialized && dynamicNavigationManager.isInitialized()) {
                val handled = dynamicNavigationManager.handleUserNavigation(R.id.healthFragment)
                if (handled) {
                    Log.d(TAG, "Navigation to HealthFragment delegated to DynamicNavigationManager")
                } else {
                    navigateToFragment(HealthFragment())
                    Log.d(TAG, "Direct navigation to HealthFragment")
                }
            } else {
                navigateToFragment(HealthFragment())
                Log.d(TAG, "Direct navigation to HealthFragment (DynamicNavigationManager not available)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error navigating to HealthFragment", e)
        }
    }

    /**
     * Navigates to Settings fragment using DynamicNavigationManager.
     */
    private fun navigateToSettingsFragment() {
        try {
            if (::dynamicNavigationManager.isInitialized && dynamicNavigationManager.isInitialized()) {
                val handled = dynamicNavigationManager.handleUserNavigation(R.id.settingsFragment)
                if (handled) {
                    Log.d(TAG, "Navigation to SettingsFragment delegated to DynamicNavigationManager")
                } else {
                    navigateToFragment(SettingsFragment())
                    Log.d(TAG, "Direct navigation to SettingsFragment")
                }
            } else {
                navigateToFragment(SettingsFragment())
                Log.d(TAG, "Direct navigation to SettingsFragment (DynamicNavigationManager not available)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error navigating to SettingsFragment", e)
        }
    }

    /**
     * Performs the actual fragment navigation with proper transaction management.
     */
    private fun navigateToFragment(targetFragment: androidx.fragment.app.Fragment) {
        try {
            parentFragmentManager.commit {
                replace(R.id.nav_host_fragment, targetFragment)
                setReorderingAllowed(true)
                // Don't add to back stack to prevent navigation stack issues
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error performing fragment navigation", e)
        }
    }

    /**
     * Prepares adapter items with dynamic charge/discharge configuration.
     * Uses proper icon and text based on current charging state.
     */
    private fun prepareAdapterItems() {
        currentDisplayItems.clear()
        Log.d(TAG, "Preparing adapter items with charging state: $isDeviceCharging")

        try {
            val chargeIcon = if (isDeviceCharging) ICON_WHEN_CHARGING else ICON_WHEN_NOT_CHARGING
            val chargeText = if (isDeviceCharging) "Discharge" else "Charge"
            val chargeDescription = if (isDeviceCharging)
                "Stop charging mode to save power or test discharge behavior"
            else
                "Activate charging mode to simulate or enable device charging"

            currentDisplayItems.addAll(
                listOf(
                    GridItemData(
                        id = CHARGE_ITEM_ID,
                        iconResId = chargeIcon,
                        text = chargeText,
                        description = chargeDescription
                    ),
                    GridItemData(
                        id = HEALTH_ITEM_ID,
                        iconResId = R.drawable.ic_health_icon,
                        text = "Health",
                        description = "Monitor battery health, temperature, and performance metrics"
                    ),
                    GridItemData(
                        id = SETTINGS_ITEM_ID,
                        iconResId = R.drawable.ic_settings_icon,
                        text = "Settings",
                        description = "Configure app preferences, notifications, and display options"
                    )
                )
            )

            Log.d(TAG, "Adapter items prepared successfully: ${currentDisplayItems.size} items")
        } catch (e: Exception) {
            Log.e(TAG, "Error preparing adapter items", e)
        }
    }

    /**
     * Updates the charge item in the list with proper change detection.
     * Checks all properties (icon, text, description) for comprehensive updates.
     */
    private fun updateChargeItemInList() {
        val chargeItemIndex = currentDisplayItems.indexOfFirst { it.id == CHARGE_ITEM_ID }
        if (chargeItemIndex != -1) {
            try {
                val oldItem = currentDisplayItems[chargeItemIndex]
                val newIcon = if (isDeviceCharging) ICON_WHEN_CHARGING else ICON_WHEN_NOT_CHARGING
                val newText = if (isDeviceCharging) "Discharge" else "Charge"
                val newDescription = if (isDeviceCharging)
                    "Stop charging mode to save power or test discharge behavior"
                else
                    "Activate charging mode to simulate or enable device charging"

                // Check if any property changed
                if (oldItem.iconResId != newIcon || oldItem.text != newText || oldItem.description != newDescription) {
                    currentDisplayItems[chargeItemIndex] = oldItem.copy(
                        iconResId = newIcon,
                        text = newText,
                        description = newDescription
                    )

                    // Use safe adapter update with proper lifecycle checks
                    if (this::gridAdapter.isInitialized && isAdded && !isDetached && view != null) {
                        gridAdapter.notifyItemChanged(chargeItemIndex)
                        Log.d(TAG, "Charge item updated: charging=$isDeviceCharging, text=$newText")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error updating charge item in list", e)
            }
        } else {
            Log.w(TAG, "Charge item not found in list for update")
        }
    }

    /**
     * Fragment lifecycle methods are now handled by CoreBatteryStatsService integration.
     * No manual BroadcastReceiver registration needed.
     */
}