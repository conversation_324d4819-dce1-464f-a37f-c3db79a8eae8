package com.tqhit.battery.one.fragment.main.others

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Resources
import android.os.BatteryManager
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentOthersBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.fragment.main.ChargeFragment
import com.tqhit.battery.one.fragment.main.DischargeFragment
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration
import com.tqhit.battery.one.fragment.main.others.adapter.GridItemData
import com.tqhit.battery.one.fragment.main.others.adapter.OthersGridAdapter
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OthersFragment : AdLibBaseFragment<FragmentOthersBinding>() {

    override val binding by lazy {
        FragmentOthersBinding.inflate(layoutInflater)
    }

    private lateinit var gridAdapter: OthersGridAdapter
    private val currentDisplayItems = mutableListOf<GridItemData>()
    private var isDeviceCharging: Boolean = false
    private val appViewModel: AppViewModel by viewModels()

    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    companion object {
        private const val CHARGE_ITEM_ID = "ChargeFragment"
        private const val DISCHARGE_ITEM_ID = "dischargeFragment"
        private const val HEALTH_ITEM_ID = "healthFragment"
        private const val SETTINGS_ITEM_ID = "settingsFragment"
        private const val TAG = "OthersFragment_Event"
    }

    private val iconWhenCharging = R.drawable.ic_discharge_icon
    private val iconWhenNotCharging = R.drawable.ic_charge_icon

    private val batteryChargeStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val status = intent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            val newChargingState = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                    status == BatteryManager.BATTERY_STATUS_FULL

            if (isDeviceCharging != newChargingState) {
                isDeviceCharging = newChargingState
                Log.d(TAG, "Charging state changed: $isDeviceCharging")
                updateChargeIconInList()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        determineInitialChargingState()
    }

    private fun determineInitialChargingState() {
        val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        val batteryStatusIntent: Intent? = requireContext().registerReceiver(null, intentFilter)
        val status = batteryStatusIntent?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
        isDeviceCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                status == BatteryManager.BATTERY_STATUS_FULL
        Log.d(TAG, "Initial charging state: $isDeviceCharging")
    }

    override fun setupUI() {
        super.setupUI()

        prepareAdapterItems()

        gridAdapter = OthersGridAdapter(currentDisplayItems) { clickedItem ->
            navigateToFragment(clickedItem.id)
        }
        setupRecyclerView()
        setupAntiThief()
        setupAntiThiefSwitches()

    }

    private fun setupRecyclerView(){
        binding.othersRecyclerView.apply {
            val spanCount = 1
            layoutManager = GridLayoutManager(requireContext(), spanCount)

            val spacingInDp = 32
            val spacingInPixels =
                (spacingInDp * Resources.getSystem().displayMetrics.density).toInt()

            if (itemDecorationCount > 0) {
                removeItemDecorationAt(0)
            }
            addItemDecoration(GridSpacingItemDecoration(spanCount, spacingInPixels))
            adapter = gridAdapter
        }
    }

    private fun setupAntiThief() {
        binding.antiThiefInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                NotificationDialog(
                    context = requireContext(),
                    title = getString(R.string.anti_thief),
                    message = getString(R.string.anti_thief_info),
                ).show()
            }
        }
    }

    private fun setupAntiThiefSwitches() {
        // Anti-Thief Enable Switch
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!appViewModel.isAntiThiefPasswordSet()) {
                    SetupPasswordDialog(
                        context = requireContext(),
                        onConfirm = { password ->
                            if (password.isNotBlank()) {
                                appViewModel.setAntiThiefPassword(password)
                                appViewModel.setAntiThiefEnabled(true)
                                binding.switchEnableAntiThief.isChecked = true
                            } else {
                                binding.switchEnableAntiThief.isChecked = false
                            }
                        },
                        onCancel = {
                            binding.switchEnableAntiThief.isChecked = false
                        }
                    ).show()
                } else {
                    appViewModel.setAntiThiefEnabled(true)
                }
            } else {
                appViewModel.setAntiThiefPassword("")
                appViewModel.setAntiThiefEnabled(false)
            }
        }

    }

    private fun navigateToFragment(itemId: String) {
        val targetFragment = when (itemId) {
            CHARGE_ITEM_ID -> {
                HealthFragment()
            }

            DISCHARGE_ITEM_ID -> {
                DischargeFragment()
            }

            HEALTH_ITEM_ID -> HealthFragment()
            SETTINGS_ITEM_ID -> SettingsFragment()
            else -> {
                OthersFragment()
            }
        }

        targetFragment?.let { fragment ->
            parentFragmentManager.commit {
                replace(R.id.nav_host_fragment, fragment)
                addToBackStack(null)
                setReorderingAllowed(true)
            }
        }
    }

    private fun prepareAdapterItems() {
        currentDisplayItems.clear()
        currentDisplayItems.addAll(
            listOf(
                GridItemData(
                    id = CHARGE_ITEM_ID,
                    text = if (isDeviceCharging) "Discharge" else "Charge",
                    description = if (isDeviceCharging)
                        "Stop charging mode to save power or test discharge behavior"
                    else
                        "Activate charging mode to simulate or enable device charging",
                    iconResId = if (isDeviceCharging) iconWhenCharging else iconWhenNotCharging
                ),
                GridItemData(
                    id = HEALTH_ITEM_ID,
                    text = "Health",
                    description = "View battery health stats including capacity, voltage, and temperature",
                    iconResId = R.drawable.ic_health_icon
                ),
                GridItemData(
                    id = SETTINGS_ITEM_ID,
                    text = "Settings",
                    description = "Customize app preferences and behavior to fit your needs",
                    iconResId = R.drawable.ic_settings_icon
                )
            )
        )
    }

    private fun updateChargeIconInList() {
        val chargeItemIndex = currentDisplayItems.indexOfFirst { it.id == CHARGE_ITEM_ID }
        if (chargeItemIndex != -1) {
            val oldItem = currentDisplayItems[chargeItemIndex]
            val newIcon = if (isDeviceCharging) iconWhenCharging else iconWhenNotCharging


            if (oldItem.iconResId != newIcon) {
                currentDisplayItems[chargeItemIndex] = oldItem.copy(iconResId = newIcon)

                if (::gridAdapter.isInitialized && binding.othersRecyclerView.adapter != null) {
                    gridAdapter.notifyItemChanged(chargeItemIndex)
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
            addAction(Intent.ACTION_BATTERY_CHANGED)
        }
        requireContext().registerReceiver(batteryChargeStateReceiver, intentFilter)

        val currentBatteryStatusIntent =
            requireContext().registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        if (currentBatteryStatusIntent != null) {
            batteryChargeStateReceiver.onReceive(requireContext(), currentBatteryStatusIntent)
        }
    }

    override fun onStop() {
        super.onStop()
        requireContext().unregisterReceiver(batteryChargeStateReceiver)
    }
}