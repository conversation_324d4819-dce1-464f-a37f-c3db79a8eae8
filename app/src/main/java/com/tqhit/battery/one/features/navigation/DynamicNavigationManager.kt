package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.others.OthersFragment
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages dynamic navigation based on real-time charging state.
 * This class coordinates fragment switching and navigation visibility
 * based on battery charging status from CoreBatteryStatsProvider.
 */
@Singleton
class DynamicNavigationManager @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "DynamicNavigationManager"
        private const val FRAGMENT_TAG_PREFIX = "nav_fragment_"
    }

    private val _navigationState = MutableStateFlow<NavigationState?>(null)
    val navigationState: StateFlow<NavigationState?> = _navigationState.asStateFlow()

    private val _stateChanges = MutableStateFlow<NavigationStateChange?>(null)
    val stateChanges: StateFlow<NavigationStateChange?> = _stateChanges.asStateFlow()

    private var isInitialized = false
    private var fragmentManager: FragmentManager? = null
    private var bottomNavigationView: BottomNavigationView? = null
    private var fragmentContainerId: Int = 0
    private var isUpdatingNavigation = false
    private var currentActiveFragment: Fragment? = null

    // Fragment cache for performance optimization
    private val fragmentCache = mutableMapOf<Int, Fragment>()

    // Fragment lifecycle optimizer
    private val lifecycleOptimizer = FragmentLifecycleOptimizer()

    // Performance tracking
    private var navigationStartTime: Long = 0
    private var fragmentCreationCount = 0
    private var cacheHitCount = 0
    private var useShowHidePattern = true // Flag to control navigation pattern
    
    /**
     * Initializes the navigation manager with required components.
     * Must be called before using other methods.
     *
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        restoredSelectedItemId: Int? = null
    ) {
        if (isInitialized) {
            Log.w(TAG, "NAVIGATION_RESTORE: DynamicNavigationManager already initialized, reinitializing for new activity instance")
            // Reset state for new activity instance
            isInitialized = false
            _navigationState.value = null
            // Clear fragment cache on reinitialization to prevent memory leaks
            clearFragmentCache()
        }

        this.fragmentManager = fragmentManager
        this.bottomNavigationView = bottomNavigationView
        this.fragmentContainerId = fragmentContainerId

        Log.d(TAG, "NAVIGATION_RESTORE: Initializing DynamicNavigationManager with restored item: $restoredSelectedItemId")

        // Start monitoring battery status changes
        startBatteryStatusMonitoring(lifecycleOwner)

        // Set up initial state with restoration context
        setupInitialState(restoredSelectedItemId)

        isInitialized = true
        Log.d(TAG, "NAVIGATION_RESTORE: DynamicNavigationManager initialized successfully")
    }
    
    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->
                    Log.d(TAG, "Battery charging state changed: $isCharging")
                    handleChargingStateChange(isCharging)
                }
        }
    }
    
    /**
     * Sets up the initial navigation state based on current battery status and restored state.
     *
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    private fun setupInitialState(restoredSelectedItemId: Int? = null) {
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        val isCharging = currentBatteryStatus?.isCharging ?: false

        Log.d(TAG, "NAVIGATION_RESTORE: Setting up initial state - charging: $isCharging, restored item: $restoredSelectedItemId")

        val initialState = if (restoredSelectedItemId != null) {
            // State restoration - create state based on restored item
            Log.d(TAG, "NAVIGATION_RESTORE: Creating state for restoration")
            when (restoredSelectedItemId) {
                R.id.chargeFragment -> NavigationState.createChargingState(shouldShowTransition = false)
                R.id.dischargeFragment -> NavigationState.createDischargingState(shouldShowTransition = false)
                R.id.animationGridFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                R.id.healthFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.healthFragment
                )
                R.id.settingsFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.settingsFragment
                )
                R.id.othersFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.othersFragment
                )
                else -> {
                    Log.w(TAG, "NAVIGATION_RESTORE: Unknown restored item $restoredSelectedItemId, falling back to animation state")
                    NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                }
            }
        } else {
            // Fresh start - use animation state as default
            Log.d(TAG, "NAVIGATION_RESTORE: Creating fresh animation state")
            NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
        }

        updateNavigationState(initialState, StateChangeReason.INITIAL_SETUP)
    }
    
    /**
     * Handles charging state changes and updates navigation accordingly.
     */
    private fun handleChargingStateChange(isCharging: Boolean) {
        val currentState = _navigationState.value
        
        // Don't update if the state hasn't actually changed
        if (currentState?.isCharging == isCharging) {
            Log.v(TAG, "Charging state unchanged, skipping update")
            return
        }
        
        val newState = if (isCharging) {
            NavigationState.createChargingState()
        } else {
            NavigationState.createDischargingState()
        }
        
        val reason = if (isCharging) StateChangeReason.CHARGING_STARTED else StateChangeReason.CHARGING_STOPPED
        updateNavigationState(newState, reason)
    }
    
    /**
     * Updates the navigation state and applies changes to UI.
     */
    private fun updateNavigationState(newState: NavigationState, reason: StateChangeReason) {
        val previousState = _navigationState.value
        _navigationState.value = newState
        
        Log.d(TAG, "Navigation state updated: ${newState.activeFragmentId} (charging: ${newState.isCharging})")
        
        // Emit state change event
        val stateChange = NavigationStateChange(previousState, newState, reason)
        _stateChanges.value = stateChange
        
        // Apply changes to UI
        applyNavigationChanges(newState, previousState)
    }
    
    /**
     * Applies navigation changes to the UI components.
     */
    private fun applyNavigationChanges(newState: NavigationState, previousState: NavigationState?) {
        try {
            // Update fragment if needed
            if (previousState?.activeFragmentId != newState.activeFragmentId) {
                switchToFragment(newState)
            }
            
            // Update bottom navigation visibility and selection
            updateBottomNavigation(newState)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying navigation changes", e)
        }
    }
    
    /**
     * Switches to the fragment specified in the navigation state using optimized caching.
     */
    private fun switchToFragment(state: NavigationState) {
        navigationStartTime = System.currentTimeMillis()

        val fragmentManager = this.fragmentManager ?: run {
            Log.e(TAG, "FragmentManager not available for fragment switch")
            return
        }

        try {
            if (useShowHidePattern) {
                performShowHideNavigation(state, fragmentManager)
            } else {
                performReplaceNavigation(state, fragmentManager)
            }

            val navigationTime = System.currentTimeMillis() - navigationStartTime
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Navigation completed in ${navigationTime}ms (cache hits: $cacheHitCount, creations: $fragmentCreationCount)")

        } catch (e: Exception) {
            Log.e(TAG, "Error switching fragment", e)
            // Fallback to replace pattern if show/hide fails
            if (useShowHidePattern) {
                Log.w(TAG, "Show/hide pattern failed, falling back to replace pattern")
                useShowHidePattern = false
                try {
                    performReplaceNavigation(state, fragmentManager)
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "Fallback replace pattern also failed", fallbackException)
                }
            }
        }
    }
    
    /**
     * Updates the bottom navigation view based on the navigation state.
     */
    private fun updateBottomNavigation(state: NavigationState) {
        val bottomNav = this.bottomNavigationView ?: run {
            Log.e(TAG, "NAVIGATION_RESTORE: BottomNavigationView not available")
            return
        }

        try {
            // Prevent infinite loop by setting flag
            isUpdatingNavigation = true

            // Update selected item
            bottomNav.selectedItemId = state.activeFragmentId
            Log.d(TAG, "NAVIGATION_RESTORE: Set selected item to: ${state.activeFragmentId}")

            // Update menu item visibility
            val menu = bottomNav.menu
            val visibleItemsCount = state.visibleMenuItems.size
            Log.d(TAG, "NAVIGATION_RESTORE: Updating menu visibility - total items: ${menu.size()}, visible items: $visibleItemsCount")

            for (i in 0 until menu.size()) {
                val menuItem = menu.getItem(i)
                val shouldBeVisible = state.isMenuItemVisible(menuItem.itemId)
                menuItem.isVisible = shouldBeVisible

                Log.d(TAG, "NAVIGATION_RESTORE: Menu item ${menuItem.itemId} visibility: $shouldBeVisible")
            }

            // Validate final state
            val actualVisibleCount = (0 until menu.size()).count { menu.getItem(it).isVisible }
            Log.d(TAG, "NAVIGATION_RESTORE: Final visible menu items count: $actualVisibleCount")

        } catch (e: Exception) {
            Log.e(TAG, "Error updating bottom navigation", e)
        } finally {
            // Reset flag
            isUpdatingNavigation = false
        }
    }
    
    /**
     * Handles user navigation selection.
     * Returns true if the navigation was handled, false otherwise.
     */
    fun handleUserNavigation(itemId: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "Navigation manager not initialized")
            return false
        }

        // Prevent infinite loop - ignore navigation events triggered by our own updates
        if (isUpdatingNavigation) {
            Log.v(TAG, "Ignoring navigation event during update")
            return true
        }

        val currentState = _navigationState.value ?: return false

        // Check if the selected item is visible in current state
        if (!currentState.isMenuItemVisible(itemId)) {
            Log.w(TAG, "User tried to navigate to hidden item: $itemId")
            return false
        }

        // Don't update if already on the same fragment
        if (currentState.activeFragmentId == itemId) {
            Log.v(TAG, "Already on fragment $itemId, skipping update")
            return true
        }

        // Create new state for user navigation
        val newState = currentState.copy(
            activeFragmentId = itemId,
            shouldShowTransition = true
        )

        updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
        return true
    }
    
    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = _navigationState.value
    
    /**
     * Performs navigation using show/hide pattern for better performance.
     */
    private fun performShowHideNavigation(state: NavigationState, fragmentManager: FragmentManager) {
        val fragment = getOrCreateFragment(state.activeFragmentId)
        val fragmentTag = getFragmentTag(state.activeFragmentId)

        Log.d(TAG, "FRAGMENT_PERFORMANCE: Show/Hide navigation to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")

        val transaction = fragmentManager.beginTransaction()

        if (state.shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        // Hide current active fragment if it exists
        currentActiveFragment?.let { activeFragment ->
            if (activeFragment.isAdded && activeFragment != fragment) {
                transaction.hide(activeFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Hiding previous fragment: ${activeFragment.javaClass.simpleName} (isVisible before hide: ${activeFragment.isVisible})")
            }
        }

        // Additionally, hide ALL other fragments to ensure no overlays (especially AnimationGridFragment)
        fragmentCache.values.forEach { cachedFragment ->
            if (cachedFragment != fragment && cachedFragment.isAdded && cachedFragment.isVisible) {
                transaction.hide(cachedFragment)
                Log.d(TAG, "FRAGMENT_PERFORMANCE: Force hiding cached fragment: ${cachedFragment.javaClass.simpleName} to prevent overlay")
            }
        }

        // Show or add the target fragment
        if (fragment.isAdded) {
            transaction.show(fragment)
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Showing cached fragment: ${fragment.javaClass.simpleName}")
        } else {
            transaction.add(fragmentContainerId, fragment, fragmentTag)
            lifecycleOptimizer.registerFragment(fragment)
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Adding new fragment: ${fragment.javaClass.simpleName}")
        }

        // Use commitNow() to ensure transaction completes before updating lifecycle state
        transaction.commitNow()

        // Verify the fragment is actually visible after commit
        val isActuallyVisible = fragment.isVisible
        Log.d(TAG, "FRAGMENT_PERFORMANCE: Fragment ${fragment.javaClass.simpleName} visibility after commit: $isActuallyVisible")

        // If fragment is not visible after commit, force it to be visible
        if (!isActuallyVisible && fragment.isAdded) {
            Log.w(TAG, "FRAGMENT_PERFORMANCE: Fragment not visible after commit, forcing visibility")
            val forceTransaction = fragmentManager.beginTransaction()
            forceTransaction.show(fragment)
            forceTransaction.commitNow()
            Log.d(TAG, "FRAGMENT_PERFORMANCE: Force visibility result: ${fragment.isVisible}")
        }

        // Update lifecycle state AFTER transaction is committed and visibility is confirmed
        currentActiveFragment?.let { previousFragment ->
            if (previousFragment != fragment && previousFragment.isAdded) {
                lifecycleOptimizer.onFragmentHidden(previousFragment)
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Notified lifecycle optimizer that ${previousFragment.javaClass.simpleName} is hidden")
            }
        }

        // Also notify lifecycle optimizer for all other hidden fragments
        fragmentCache.values.forEach { cachedFragment ->
            if (cachedFragment != fragment && cachedFragment.isAdded && !cachedFragment.isVisible) {
                lifecycleOptimizer.onFragmentHidden(cachedFragment)
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Ensured ${cachedFragment.javaClass.simpleName} is marked as hidden in lifecycle optimizer")
            }
        }

        // Update current active fragment reference and notify lifecycle optimizer
        currentActiveFragment = fragment
        lifecycleOptimizer.onFragmentVisible(fragment)

        // Validate fragment visibility to ensure no overlapping
        validateFragmentVisibility()

        // Additional check specifically for AnimationGridFragment overlay issues
        fixAnimationGridFragmentOverlay(fragment)

        // Verify fragment visibility state with detailed logging
        val visibleFragments = fragmentCache.values.filter { it.isVisible }
        val hiddenFragments = fragmentCache.values.filter { it.isHidden }

        Log.d(TAG, "FRAGMENT_VISIBILITY: Visible fragments: ${visibleFragments.size}, Hidden fragments: ${hiddenFragments.size}, Target: ${fragment.javaClass.simpleName} visible=${fragment.isVisible}")

        // Log each fragment's visibility state for debugging
        fragmentCache.values.forEach { cachedFragment ->
            Log.d(TAG, "FRAGMENT_VISIBILITY: ${cachedFragment.javaClass.simpleName} - isVisible: ${cachedFragment.isVisible}, isHidden: ${cachedFragment.isHidden}, isAdded: ${cachedFragment.isAdded}")
        }
    }

    /**
     * Performs navigation using replace pattern as fallback.
     */
    private fun performReplaceNavigation(state: NavigationState, fragmentManager: FragmentManager) {
        val fragment = getOrCreateFragment(state.activeFragmentId)

        Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation to: ${fragment.javaClass.simpleName} (cached: ${fragmentCache.containsKey(state.activeFragmentId)})")

        val transaction = fragmentManager.beginTransaction()

        if (state.shouldShowTransition) {
            transaction.setCustomAnimations(
                android.R.anim.fade_in,
                android.R.anim.fade_out
            )
        }

        transaction.replace(fragmentContainerId, fragment)
        transaction.commitAllowingStateLoss()

        // Update current active fragment reference
        currentActiveFragment = fragment
        lifecycleOptimizer.onFragmentVisible(fragment)

        Log.d(TAG, "FRAGMENT_PERFORMANCE: Replace navigation completed for: ${fragment.javaClass.simpleName}")
    }

    /**
     * Checks if the navigation manager is initialized.
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * Gets or creates a fragment instance with caching for performance optimization.
     */
    private fun getOrCreateFragment(fragmentId: Int): Fragment {
        // Check cache first
        fragmentCache[fragmentId]?.let { cachedFragment ->
            cacheHitCount++
            Log.d(TAG, "FRAGMENT_CACHE: Cache hit for fragment ID: $fragmentId")
            return cachedFragment
        }

        // Create new fragment if not in cache
        val fragment = createFragmentInstance(fragmentId)
        fragmentCache[fragmentId] = fragment
        fragmentCreationCount++

        Log.d(TAG, "FRAGMENT_CACHE: Created new fragment for ID: $fragmentId (total cached: ${fragmentCache.size})")
        return fragment
    }

    /**
     * Creates a new fragment instance based on fragment ID.
     */
    private fun createFragmentInstance(fragmentId: Int): Fragment {
        return when (fragmentId) {
            R.id.chargeFragment -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
            R.id.dischargeFragment -> com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment()
            R.id.healthFragment -> com.tqhit.battery.one.fragment.main.HealthFragment()
            R.id.settingsFragment -> com.tqhit.battery.one.fragment.main.SettingsFragment()
            R.id.animationGridFragment -> com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment()
            R.id.othersFragment -> OthersFragment()

            else -> com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment()
        }
    }

    /**
     * Generates a unique tag for fragment identification.
     */
    private fun getFragmentTag(fragmentId: Int): String {
        return "$FRAGMENT_TAG_PREFIX$fragmentId"
    }

    /**
     * Clears the fragment cache and removes fragments from FragmentManager.
     */
    private fun clearFragmentCache() {
        val fragmentManager = this.fragmentManager
        if (fragmentManager != null && fragmentCache.isNotEmpty()) {
            Log.d(TAG, "FRAGMENT_CACHE: Clearing fragment cache (${fragmentCache.size} fragments)")

            val transaction = fragmentManager.beginTransaction()
            fragmentCache.values.forEach { fragment ->
                if (fragment.isAdded) {
                    transaction.remove(fragment)
                }
            }
            transaction.commitAllowingStateLoss()
        }

        fragmentCache.clear()
        currentActiveFragment = null
        fragmentCreationCount = 0
        cacheHitCount = 0
        lifecycleOptimizer.clear()

        Log.d(TAG, "FRAGMENT_CACHE: Fragment cache cleared")
    }

    /**
     * Validates and fixes fragment visibility states to ensure only one fragment is visible.
     * Enhanced to specifically handle AnimationGridFragment overlay issues.
     */
    private fun validateFragmentVisibility() {
        val fragmentManager = this.fragmentManager ?: return

        // Log ALL fragments in the container to detect Navigation Component conflicts
        val allFragments = fragmentManager.fragments
        Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: Total fragments in container: ${allFragments.size}")
        allFragments.forEachIndexed { index, fragment ->
            Log.d(TAG, "FRAGMENT_CONTAINER_AUDIT: [$index] ${fragment?.javaClass?.simpleName} - visible: ${fragment?.isVisible}, hidden: ${fragment?.isHidden}, added: ${fragment?.isAdded}")
        }

        // Remove any unknown fragments that might be from Navigation Component
        val unknownFragments = allFragments.filter { fragment ->
            fragment != null && fragment.isAdded && !fragmentCache.values.contains(fragment)
        }

        if (unknownFragments.isNotEmpty()) {
            Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Found ${unknownFragments.size} unknown fragments (likely from Navigation Component), removing...")
            val cleanupTransaction = fragmentManager.beginTransaction()
            unknownFragments.forEach { fragment ->
                Log.w(TAG, "FRAGMENT_CONTAINER_AUDIT: Removing unknown fragment: ${fragment?.javaClass?.simpleName}")
                if (fragment != null) {
                    cleanupTransaction.remove(fragment)
                }
            }
            cleanupTransaction.commitNow()
        }

        try {
            val visibleFragments = fragmentCache.values.filter { it.isVisible && it.isAdded }
            val currentActiveFragmentName = currentActiveFragment?.javaClass?.simpleName ?: "none"

            Log.d(TAG, "FRAGMENT_VISIBILITY: Validating visibility - Active: $currentActiveFragmentName, Visible count: ${visibleFragments.size}")

            // Log all fragment states for debugging
            fragmentCache.values.forEach { fragment ->
                Log.d(TAG, "FRAGMENT_VISIBILITY: ${fragment.javaClass.simpleName} - visible: ${fragment.isVisible}, hidden: ${fragment.isHidden}, added: ${fragment.isAdded}")
            }

            if (visibleFragments.size > 1) {
                Log.w(TAG, "FRAGMENT_VISIBILITY: Multiple fragments visible (${visibleFragments.size}), fixing...")

                val transaction = fragmentManager.beginTransaction()
                visibleFragments.forEach { fragment ->
                    if (fragment != currentActiveFragment) {
                        transaction.hide(fragment)
                        lifecycleOptimizer.onFragmentHidden(fragment)
                        Log.w(TAG, "FRAGMENT_VISIBILITY: Force hiding ${fragment.javaClass.simpleName} to fix overlay")
                    }
                }
                transaction.commitNow()

                Log.d(TAG, "FRAGMENT_VISIBILITY: Fixed multiple visibility issue - only ${currentActiveFragmentName} should now be visible")
            } else if (visibleFragments.size == 0 && currentActiveFragment != null) {
                Log.w(TAG, "FRAGMENT_VISIBILITY: No fragments visible but should have active fragment, fixing...")

                val transaction = fragmentManager.beginTransaction()
                currentActiveFragment?.let { activeFragment ->
                    if (activeFragment.isAdded) {
                        transaction.show(activeFragment)
                        transaction.commitNow()
                        Log.d(TAG, "FRAGMENT_VISIBILITY: Restored visibility for ${activeFragment.javaClass.simpleName}")
                    }
                }
            }

            // Final verification
            val finalVisibleCount = fragmentCache.values.count { it.isVisible && it.isAdded }
            if (finalVisibleCount != 1) {
                Log.e(TAG, "FRAGMENT_VISIBILITY: Validation failed - $finalVisibleCount fragments visible after fix attempt")
            } else {
                Log.d(TAG, "FRAGMENT_VISIBILITY: Validation successful - exactly 1 fragment visible")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error validating fragment visibility", e)
        }
    }

    /**
     * Specifically handles AnimationGridFragment overlay issues.
     * This method ensures AnimationGridFragment is properly hidden when other fragments are active.
     */
    private fun fixAnimationGridFragmentOverlay(targetFragment: Fragment) {
        val fragmentManager = this.fragmentManager ?: return

        try {
            // Find AnimationGridFragment in cache
            val animationFragment = fragmentCache.values.find {
                it.javaClass.simpleName == "AnimationGridFragment"
            }

            if (animationFragment != null && animationFragment != targetFragment) {
                if (animationFragment.isVisible && animationFragment.isAdded) {
                    Log.w(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is visible when ${targetFragment.javaClass.simpleName} should be active - forcing hide")

                    val transaction = fragmentManager.beginTransaction()
                    transaction.hide(animationFragment)
                    transaction.commitNow()

                    lifecycleOptimizer.onFragmentHidden(animationFragment)

                    Log.d(TAG, "ANIMATION_OVERLAY_FIX: Successfully hid AnimationGridFragment overlay")
                } else {
                    Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is properly hidden (visible: ${animationFragment.isVisible}, added: ${animationFragment.isAdded})")
                }
            } else if (animationFragment == targetFragment) {
                Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment is the target fragment - no overlay fix needed")
            } else {
                Log.d(TAG, "ANIMATION_OVERLAY_FIX: AnimationGridFragment not found in cache")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fixing AnimationGridFragment overlay", e)
        }
    }

    /**
     * Gets performance statistics for debugging.
     */
    fun getPerformanceStats(): String {
        val cacheStats = "Fragment Cache Stats - Cached: ${fragmentCache.size}, Created: $fragmentCreationCount, Cache Hits: $cacheHitCount, Hit Rate: ${if (fragmentCreationCount > 0) (cacheHitCount * 100 / (fragmentCreationCount + cacheHitCount)) else 0}%"
        val lifecycleStats = lifecycleOptimizer.getLifecycleStats()
        val visibilityStats = "Fragment Visibility - Active: ${currentActiveFragment?.javaClass?.simpleName ?: "none"}, Visible: ${fragmentCache.values.count { it.isVisible }}, Hidden: ${fragmentCache.values.count { it.isHidden }}"
        return "$cacheStats\n$lifecycleStats\n$visibilityStats"
    }
}
