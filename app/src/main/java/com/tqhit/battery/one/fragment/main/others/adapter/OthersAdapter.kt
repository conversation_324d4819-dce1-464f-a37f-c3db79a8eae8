package com.tqhit.battery.one.fragment.main.others.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.tbuonomo.viewpagerdotsindicator.setBackgroundCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemLayoutOthersBinding

data class GridItemData(
    val id: String,
    val text: String,
    val description: String,
    val iconResId: Int
)

class OthersGridAdapter(
    private val items: List<GridItemData>,
    private val onItemClick: (GridItemData) -> Unit
) :
    RecyclerView.Adapter<OthersGridAdapter.ViewHolder>() {

    companion object {
        private const val CHARGE_ITEM_ID = "ChargeFragment"
        private const val DISCHARGE_ITEM_ID = "dischargeFragment"
        private const val HEALTH_ITEM_ID = "healthFragment"
        private const val SETTINGS_ITEM_ID = "settingsFragment"
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            ItemLayoutOthersBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)

        holder.itemView.setOnClickListener {
            onItemClick(item)
        }

    }

    override fun getItemCount(): Int = items.size


    inner class ViewHolder(private val binding: ItemLayoutOthersBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(item: GridItemData) {
            binding.itemTitle.text = item.text
            binding.itemDesc.text = item.description
            binding.itemIcon.setImageResource(item.iconResId)

            val backgroundRes = when (item.id) {
                CHARGE_ITEM_ID -> R.drawable.scattered_forcefields
                DISCHARGE_ITEM_ID -> R.drawable.scattered_forcefields
                HEALTH_ITEM_ID -> R.drawable.wintery_sunburst
                SETTINGS_ITEM_ID -> R.drawable.alternating_arrowhead
                else -> R.drawable.wave_3
            }

            binding.contentContainer.setBackgroundResource(backgroundRes)
        }
    }
}
